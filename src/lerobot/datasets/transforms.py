#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import collections
import inspect
import os
from dataclasses import dataclass, field
from typing import Any, Callable, Sequence

import torch
import numpy as np
from torchvision.transforms import v2
from torchvision.transforms.v2 import Transform
from torchvision.transforms.v2 import functional as F  # noqa: N812


class RandomSubsetApply(Transform):
    """Apply a random subset of N transformations from a list of transformations.

    Args:
        transforms: list of transformations.
        p: represents the multinomial probabilities (with no replacement) used for sampling the transform.
            If the sum of the weights is not 1, they will be normalized. If ``None`` (default), all transforms
            have the same probability.
        n_subset: number of transformations to apply. If ``None``, all transforms are applied.
            Must be in [1, len(transforms)].
        random_order: apply transformations in a random order.
    """

    def __init__(
        self,
        transforms: Sequence[Callable],
        p: list[float] | None = None,
        n_subset: int | None = None,
        random_order: bool = False,
    ) -> None:
        super().__init__()
        if not isinstance(transforms, Sequence):
            raise TypeError("Argument transforms should be a sequence of callables")
        if p is None:
            p = [1] * len(transforms)
        elif len(p) != len(transforms):
            raise ValueError(
                f"Length of p doesn't match the number of transforms: {len(p)} != {len(transforms)}"
            )

        if n_subset is None:
            n_subset = len(transforms)
        elif not isinstance(n_subset, int):
            raise TypeError("n_subset should be an int or None")
        elif not (1 <= n_subset <= len(transforms)):
            raise ValueError(f"n_subset should be in the interval [1, {len(transforms)}]")

        self.transforms = transforms
        total = sum(p)
        self.p = [prob / total for prob in p]
        self.n_subset = n_subset
        self.random_order = random_order

        self.selected_transforms = None

    def forward(self, *inputs: Any) -> Any:
        needs_unpacking = len(inputs) > 1

        selected_indices = torch.multinomial(torch.tensor(self.p), self.n_subset)
        if not self.random_order:
            selected_indices = selected_indices.sort().values

        self.selected_transforms = [self.transforms[i] for i in selected_indices]

        for transform in self.selected_transforms:
            outputs = transform(*inputs)
            inputs = outputs if needs_unpacking else (outputs,)

        return outputs

    def extra_repr(self) -> str:
        return (
            f"transforms={self.transforms}, "
            f"p={self.p}, "
            f"n_subset={self.n_subset}, "
            f"random_order={self.random_order}"
        )


class SharpnessJitter(Transform):
    """Randomly change the sharpness of an image or video.

    Similar to a v2.RandomAdjustSharpness with p=1 and a sharpness_factor sampled randomly.
    While v2.RandomAdjustSharpness applies — with a given probability — a fixed sharpness_factor to an image,
    SharpnessJitter applies a random sharpness_factor each time. This is to have a more diverse set of
    augmentations as a result.

    A sharpness_factor of 0 gives a blurred image, 1 gives the original image while 2 increases the sharpness
    by a factor of 2.

    If the input is a :class:`torch.Tensor`,
    it is expected to have [..., 1 or 3, H, W] shape, where ... means an arbitrary number of leading dimensions.

    Args:
        sharpness: How much to jitter sharpness. sharpness_factor is chosen uniformly from
            [max(0, 1 - sharpness), 1 + sharpness] or the given
            [min, max]. Should be non negative numbers.
    """

    def __init__(self, sharpness: float | Sequence[float]) -> None:
        super().__init__()
        self.sharpness = self._check_input(sharpness)

    def _check_input(self, sharpness):
        if isinstance(sharpness, (int, float)):
            if sharpness < 0:
                raise ValueError("If sharpness is a single number, it must be non negative.")
            sharpness = [1.0 - sharpness, 1.0 + sharpness]
            sharpness[0] = max(sharpness[0], 0.0)
        elif isinstance(sharpness, collections.abc.Sequence) and len(sharpness) == 2:
            sharpness = [float(v) for v in sharpness]
        else:
            raise TypeError(f"{sharpness=} should be a single number or a sequence with length 2.")

        if not 0.0 <= sharpness[0] <= sharpness[1]:
            raise ValueError(f"sharpness values should be between (0., inf), but got {sharpness}.")

        return float(sharpness[0]), float(sharpness[1])

    def make_params(self, flat_inputs: list[Any]) -> dict[str, Any]:
        sharpness_factor = torch.empty(1).uniform_(self.sharpness[0], self.sharpness[1]).item()
        return {"sharpness_factor": sharpness_factor}

    def transform(self, inpt: Any, params: dict[str, Any]) -> Any:
        sharpness_factor = params["sharpness_factor"]
        return self._call_kernel(F.adjust_sharpness, inpt, sharpness_factor=sharpness_factor)


class SaveTransformedImages(Transform):
    """Save transformed images at specific training steps for debugging purposes.

    This transform saves the images after all other transforms have been applied,
    allowing you to see what the model actually receives as input.

    Args:
        save_dir: Directory to save the images
        target_steps: List of training steps at which to save images
        save_original: Whether to also save the original (untransformed) image
    """

    # Class variables to track global state
    _global_step_counter = 0
    _saved_steps_per_camera = {}
    _save_executor = None

    def __init__(self, save_dir: str = "transformed_images", target_steps: list[int] = None, save_original: bool = False) -> None:
        super().__init__()
        self.save_dir = save_dir
        self.target_steps = target_steps if target_steps is not None else [0]  # 只保存步骤0
        self.save_original = save_original

        os.makedirs(self.save_dir, exist_ok=True)

        # Initialize thread pool for async saving
        if SaveTransformedImages._save_executor is None:
            import concurrent.futures
            SaveTransformedImages._save_executor = concurrent.futures.ThreadPoolExecutor(
                max_workers=2, thread_name_prefix="SaveTransformedImages"
            )

        print(f"📸 SaveTransformedImages: 将在步骤 {self.target_steps} 保存变换后的图像到 {self.save_dir}/")

    def forward(self, *inputs: Any) -> Any:
        # Apply this transform at the end of the transform chain
        # So we get the final transformed image that goes to the model

        # Check if we should save at this step (use current step, not incremented step)
        current_step = SaveTransformedImages._global_step_counter
        should_save = (current_step in self.target_steps and
                      len(inputs) > 0 and isinstance(inputs[0], torch.Tensor))

        if should_save:
            # Get camera name from call stack
            camera_name = self._get_camera_name_from_stack()

            # Create a unique key for this step and camera combination
            step_camera_key = f"step_{current_step}_camera_{camera_name}"

            # Check if this specific step-camera combination has already been saved
            if step_camera_key not in SaveTransformedImages._saved_steps_per_camera:
                SaveTransformedImages._saved_steps_per_camera[step_camera_key] = True

                # Save the transformed image asynchronously with batch index
                self._async_save_image_with_batch(inputs[0], camera_name, "transformed", current_step)

        # Return inputs unchanged (this is a monitoring transform)
        return inputs[0] if len(inputs) == 1 else inputs

    def _get_camera_name_from_stack(self) -> str:
        """Extract camera name from the call stack."""
        try:
            frame = inspect.currentframe()
            while frame:
                frame_locals = frame.f_locals
                # Look for camera-related variables in the call stack
                for var_name, var_value in frame_locals.items():
                    if 'cam' in var_name.lower() and isinstance(var_value, str):
                        return var_value
                    elif var_name == 'cam' and isinstance(var_value, str):
                        return var_value
                frame = frame.f_back
            return "unknown_camera"
        except Exception:
            return "unknown_camera"

    def _async_save_image_with_batch(self, img: torch.Tensor, camera_name: str, image_type: str, step: int) -> None:
        """Asynchronously save image batch to avoid blocking training."""
        if SaveTransformedImages._save_executor is not None:
            SaveTransformedImages._save_executor.submit(self._save_image_batch, img.clone(), camera_name, image_type, step)

    def _save_image_batch(self, img: torch.Tensor, camera_name: str, image_type: str, step: int) -> None:
        """Save the entire batch of images to disk."""
        try:
            from torchvision.transforms import ToPILImage

            to_pil = ToPILImage()

            # Handle batch data: save all samples in the batch
            if img.dim() == 4:  # BCHW format (batch)
                batch_size = img.shape[0]
                for batch_idx in range(min(batch_size, 8)):  # 最多保存8张图像
                    img_to_save = img[batch_idx]

                    # Move to CPU to save GPU memory
                    img_cpu = img_to_save.detach().cpu()

                    # Ensure image values are in [0,1] range
                    img_norm = torch.clamp(img_cpu, 0, 1)

                    # Clean camera name for filename
                    clean_camera_name = camera_name.replace(".", "_").replace("/", "_").replace("\\", "_")

                    # Save image with batch index
                    img_pil = to_pil(img_norm)
                    save_path = f"{self.save_dir}/step_{step:03d}_{clean_camera_name}_batch_{batch_idx:02d}_{image_type}.png"
                    img_pil.save(save_path)

                print(f"📸 步骤 {step} ({camera_name}): 保存了 {min(batch_size, 8)} 张{image_type}图像到 {self.save_dir}/")

            elif img.dim() == 3:  # CHW format (single image)
                # Move to CPU to save GPU memory
                img_cpu = img.detach().cpu()

                # Ensure image values are in [0,1] range
                img_norm = torch.clamp(img_cpu, 0, 1)

                # Clean camera name for filename
                clean_camera_name = camera_name.replace(".", "_").replace("/", "_").replace("\\", "_")

                # Save single image
                img_pil = to_pil(img_norm)
                save_path = f"{self.save_dir}/step_{step:03d}_{clean_camera_name}_{image_type}.png"
                img_pil.save(save_path)

                print(f"📸 步骤 {step} ({camera_name}): {image_type}图像已保存到 {save_path}")
            else:
                print(f"⚠️  不支持的图像维度: {img.dim()}D")
                return

        except Exception as e:
            print(f"⚠️  保存{image_type}图像时出错: {e}")

    @classmethod
    def increment_global_step(cls):
        """Increment the global step counter."""
        cls._global_step_counter += 1

    @classmethod
    def cleanup(cls):
        """Clean up resources."""
        if cls._save_executor is not None:
            cls._save_executor.shutdown(wait=True)
            cls._save_executor = None


@dataclass
class ImageTransformConfig:
    """
    For each transform, the following parameters are available:
      weight: This represents the multinomial probability (with no replacement)
            used for sampling the transform. If the sum of the weights is not 1,
            they will be normalized.
      type: The name of the class used. This is either a class available under torchvision.transforms.v2 or a
            custom transform defined here.
      kwargs: Lower & upper bound respectively used for sampling the transform's parameter
            (following uniform distribution) when it's applied.
    """

    weight: float = 1.0
    type: str = "Identity"
    kwargs: dict[str, Any] = field(default_factory=dict)


@dataclass
class ImageTransformsConfig:
    """
    These transforms are all using standard torchvision.transforms.v2
    You can find out how these transformations affect images here:
    https://pytorch.org/vision/0.18/auto_examples/transforms/plot_transforms_illustrations.html
    We use a custom RandomSubsetApply container to sample them.
    """

    # Set this flag to `true` to enable transforms during training
    enable: bool = False
    # This is the maximum number of transforms (sampled from these below) that will be applied to each frame.
    # It's an integer in the interval [1, number_of_available_transforms].
    max_num_transforms: int = 3
    # By default, transforms are applied in Torchvision's suggested order (shown below).
    # Set this to True to apply them in a random order.
    random_order: bool = False

    # Enable saving transformed images (save images after transforms are applied)
    enable_save_transformed: bool = False
    # Directory to save transformed images
    save_transformed_dir: str = "transformed_images"
    # Steps at which to save transformed images
    save_transformed_steps: list[int] = field(default_factory=lambda: [0, 5, 15, 25])
    # Whether to also save original images for comparison
    save_original_images: bool = False
    tfs: dict[str, ImageTransformConfig] = field(
        default_factory=lambda: {
            "brightness": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"brightness": (0.8, 1.2)},
            ),
            "contrast": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"contrast": (0.8, 1.2)},
            ),
            "saturation": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"saturation": (0.5, 1.5)},
            ),
            "hue": ImageTransformConfig(
                weight=1.0,
                type="ColorJitter",
                kwargs={"hue": (-0.05, 0.05)},
            ),
            "sharpness": ImageTransformConfig(
                weight=1.0,
                type="SharpnessJitter",
                kwargs={"sharpness": (0.5, 1.5)},
            ),
            "save_transformed_images": ImageTransformConfig(
                weight=0.0,  # 默认禁用，通过 enable_save_transformed 控制
                type="SaveTransformedImages",
                kwargs={"save_dir": "transformed_images", "target_steps": [0, 5, 15, 25], "save_original": False},
            ),
        }
    )


def make_transform_from_config(cfg: ImageTransformConfig):
    if cfg.type == "Identity":
        return v2.Identity(**cfg.kwargs)
    elif cfg.type == "ColorJitter":
        return v2.ColorJitter(**cfg.kwargs)
    elif cfg.type == "SharpnessJitter":
        return SharpnessJitter(**cfg.kwargs)
    elif cfg.type == "SaveTransformedImages":
        return SaveTransformedImages(**cfg.kwargs)
    else:
        raise ValueError(f"Transform '{cfg.type}' is not valid.")


class ImageTransforms(Transform):
    """A class to compose image transforms based on configuration."""

    def __init__(self, cfg: ImageTransformsConfig) -> None:
        super().__init__()
        self._cfg = cfg

        self.weights = []
        self.transforms = {}
        self.always_apply_transforms = []  # 始终应用的变换

        # 根据配置参数动态调整变换权重和参数
        modified_tfs = dict(cfg.tfs)

        # 处理保存变换后图像的变换
        if "save_transformed_images" in modified_tfs:
            if cfg.enable_save_transformed:
                # 启用保存变换后图像
                modified_tfs["save_transformed_images"] = ImageTransformConfig(
                    weight=1.0,
                    type="SaveTransformedImages",
                    kwargs={
                        "save_dir": cfg.save_transformed_dir,
                        "target_steps": cfg.save_transformed_steps,
                        "save_original": cfg.save_original_images
                    }
                )
                # 这个变换应该始终应用，且在最后应用
                self.always_apply_transforms.append(make_transform_from_config(modified_tfs["save_transformed_images"]))
            else:
                # 禁用保存变换后图像
                modified_tfs["save_transformed_images"] = ImageTransformConfig(
                    weight=0.0,
                    type="SaveTransformedImages",
                    kwargs={
                        "save_dir": cfg.save_transformed_dir,
                        "target_steps": cfg.save_transformed_steps,
                        "save_original": cfg.save_original_images
                    }
                )

        # 处理其他变换（排除保存变换后图像的变换）
        for tf_name, tf_cfg in modified_tfs.items():
            if tf_name == "save_transformed_images":
                continue  # 已经在上面处理过了
            if tf_cfg.weight <= 0.0:
                continue

            self.transforms[tf_name] = make_transform_from_config(tf_cfg)
            self.weights.append(tf_cfg.weight)

        n_subset = min(len(self.transforms), cfg.max_num_transforms)
        if n_subset == 0 or not cfg.enable:
            self.main_tf = v2.Identity()
        else:
            self.main_tf = RandomSubsetApply(
                transforms=list(self.transforms.values()),
                p=self.weights,
                n_subset=n_subset,
                random_order=cfg.random_order,
            )

    def forward(self, *inputs: Any) -> Any:
        # 首先应用主要的变换
        outputs = self.main_tf(*inputs)

        # 然后应用始终应用的变换（如保存图像）
        for transform in self.always_apply_transforms:
            if isinstance(outputs, tuple):
                outputs = transform(*outputs)
            else:
                outputs = transform(outputs)

        return outputs
      


def convert_absolute_to_relative_actions(
    actions: torch.Tensor | np.ndarray,
    current_state: torch.Tensor | np.ndarray,
    chunk_size: int,
    action_dim: int | None = None,
    return_type: str = "torch"
) -> torch.Tensor | np.ndarray:
    """
    通用函数：将绝对关节值转换为相对关节值 (action[n] - current)

    Args:
        actions: 绝对动作序列，形状为 [seq_len, action_dim] 或 [batch_size, seq_len, action_dim]
        current_state: 当前状态，形状为 [action_dim] 或 [batch_size, action_dim]
        chunk_size: chunk大小，用于分块处理
        action_dim: 动作维度，如果为None则自动推断
        return_type: 返回类型，"torch" 或 "numpy"

    Returns:
        相对动作序列，形状与输入actions相同

    Examples:
        >>> # 单个序列
        >>> actions = torch.tensor([[1.0, 2.0], [1.5, 2.5], [2.0, 3.0]])  # [3, 2]
        >>> current = torch.tensor([0.5, 1.5])  # [2]
        >>> relative = convert_absolute_to_relative_actions(actions, current, chunk_size=2)
        >>> # 结果: [[0.5, 0.5], [1.0, 1.0], [1.5, 1.5]]

        >>> # 批量处理
        >>> actions = torch.tensor([[[1.0, 2.0], [1.5, 2.5]], [[2.0, 3.0], [2.5, 3.5]]])  # [2, 2, 2]
        >>> current = torch.tensor([[0.5, 1.5], [1.0, 2.0]])  # [2, 2]
        >>> relative = convert_absolute_to_relative_actions(actions, current, chunk_size=2)
    """
    # 输入验证和类型转换
    if isinstance(actions, np.ndarray):
        actions = torch.from_numpy(actions).float()
        was_numpy = True
    else:
        was_numpy = False
        actions = actions.float()

    if isinstance(current_state, np.ndarray):
        current_state = torch.from_numpy(current_state).float()
    else:
        current_state = current_state.float()

    # 确定维度
    if actions.dim() == 2:
        # [seq_len, action_dim]
        seq_len, inferred_action_dim = actions.shape
        batch_size = 1
        actions = actions.unsqueeze(0)  # [1, seq_len, action_dim]
        current_state = current_state.unsqueeze(0) if current_state.dim() == 1 else current_state
    elif actions.dim() == 3:
        # [batch_size, seq_len, action_dim]
        batch_size, seq_len, inferred_action_dim = actions.shape
    else:
        raise ValueError(f"actions必须是2D或3D tensor，得到形状: {actions.shape}")

    if action_dim is None:
        action_dim = inferred_action_dim
    elif action_dim != inferred_action_dim:
        raise ValueError(f"指定的action_dim ({action_dim}) 与推断的维度 ({inferred_action_dim}) 不匹配")

    # 验证current_state形状
    if current_state.dim() == 1:
        if current_state.shape[0] != action_dim:
            raise ValueError(f"current_state维度 ({current_state.shape[0]}) 与action_dim ({action_dim}) 不匹配")
        current_state = current_state.unsqueeze(0).expand(batch_size, -1)  # [batch_size, action_dim]
    elif current_state.dim() == 2:
        if current_state.shape != (batch_size, action_dim):
            raise ValueError(f"current_state形状 ({current_state.shape}) 与期望的 ({batch_size}, {action_dim}) 不匹配")
    else:
        raise ValueError(f"current_state必须是1D或2D tensor，得到形状: {current_state.shape}")

    # 计算相对动作
    relative_actions = torch.zeros_like(actions)

    for batch_idx in range(batch_size):
        batch_actions = actions[batch_idx]  # [seq_len, action_dim]
        batch_current = current_state[batch_idx]  # [action_dim]

        # 按chunk_size分块处理
        for chunk_start in range(0, seq_len, chunk_size):
            chunk_end = min(chunk_start + chunk_size, seq_len)
            chunk_actions = batch_actions[chunk_start:chunk_end]  # [chunk_len, action_dim]

            # 计算相对动作: action[n] - current
            # 对于每个chunk，都相对于当前状态计算
            relative_chunk = chunk_actions - batch_current.unsqueeze(0)  # [chunk_len, action_dim]
            relative_actions[batch_idx, chunk_start:chunk_end] = relative_chunk

    # 恢复原始形状
    if batch_size == 1 and actions.dim() == 3:
        relative_actions = relative_actions.squeeze(0)  # [seq_len, action_dim]

    # 返回指定类型
    if return_type == "numpy" or was_numpy:
        return relative_actions.numpy()
    else:
        return relative_actions


def convert_relative_to_absolute_actions(
    relative_actions: torch.Tensor | np.ndarray,
    current_state: torch.Tensor | np.ndarray,
    chunk_size: int,
    action_dim: int | None = None,
    return_type: str = "torch"
) -> torch.Tensor | np.ndarray:
    """
    通用函数：将相对关节值转换回绝对关节值 (relative + current)

    Args:
        relative_actions: 相对动作序列，形状为 [seq_len, action_dim] 或 [batch_size, seq_len, action_dim]
        current_state: 当前状态，形状为 [action_dim] 或 [batch_size, action_dim]
        chunk_size: chunk大小，用于分块处理
        action_dim: 动作维度，如果为None则自动推断
        return_type: 返回类型，"torch" 或 "numpy"

    Returns:
        绝对动作序列，形状与输入relative_actions相同
    """
    # 输入验证和类型转换
    if isinstance(relative_actions, np.ndarray):
        relative_actions = torch.from_numpy(relative_actions).float()
        was_numpy = True
    else:
        was_numpy = False
        relative_actions = relative_actions.float()

    if isinstance(current_state, np.ndarray):
        current_state = torch.from_numpy(current_state).float()
    else:
        current_state = current_state.float()

    # 确定维度
    if relative_actions.dim() == 2:
        # [seq_len, action_dim]
        seq_len, inferred_action_dim = relative_actions.shape
        batch_size = 1
        relative_actions = relative_actions.unsqueeze(0)  # [1, seq_len, action_dim]
        current_state = current_state.unsqueeze(0) if current_state.dim() == 1 else current_state
    elif relative_actions.dim() == 3:
        # [batch_size, seq_len, action_dim]
        batch_size, seq_len, inferred_action_dim = relative_actions.shape
    else:
        raise ValueError(f"relative_actions必须是2D或3D tensor，得到形状: {relative_actions.shape}")

    if action_dim is None:
        action_dim = inferred_action_dim
    elif action_dim != inferred_action_dim:
        raise ValueError(f"指定的action_dim ({action_dim}) 与推断的维度 ({inferred_action_dim}) 不匹配")

    # 验证current_state形状
    if current_state.dim() == 1:
        if current_state.shape[0] != action_dim:
            raise ValueError(f"current_state维度 ({current_state.shape[0]}) 与action_dim ({action_dim}) 不匹配")
        current_state = current_state.unsqueeze(0).expand(batch_size, -1)  # [batch_size, action_dim]
    elif current_state.dim() == 2:
        if current_state.shape != (batch_size, action_dim):
            raise ValueError(f"current_state形状 ({current_state.shape}) 与期望的 ({batch_size}, {action_dim}) 不匹配")
    else:
        raise ValueError(f"current_state必须是1D或2D tensor，得到形状: {current_state.shape}")

    # 计算绝对动作
    absolute_actions = torch.zeros_like(relative_actions)

    for batch_idx in range(batch_size):
        batch_relative = relative_actions[batch_idx]  # [seq_len, action_dim]
        batch_current = current_state[batch_idx]  # [action_dim]

        # 按chunk_size分块处理
        for chunk_start in range(0, seq_len, chunk_size):
            chunk_end = min(chunk_start + chunk_size, seq_len)
            chunk_relative = batch_relative[chunk_start:chunk_end]  # [chunk_len, action_dim]

            # 计算绝对动作: relative + current
            absolute_chunk = chunk_relative + batch_current.unsqueeze(0)  # [chunk_len, action_dim]
            absolute_actions[batch_idx, chunk_start:chunk_end] = absolute_chunk

    # 恢复原始形状
    if batch_size == 1 and relative_actions.dim() == 3:
        absolute_actions = absolute_actions.squeeze(0)  # [seq_len, action_dim]

    # 返回指定类型
    if return_type == "numpy" or was_numpy:
        return absolute_actions.numpy()
    else:
        return absolute_actions
