#!/usr/bin/env python3

"""
测试分布式训练功能的简单脚本
"""

import os
import subprocess
import sys
import time

def check_gpu_availability():
    """检查GPU可用性"""
    try:
        result = subprocess.run(['nvidia-smi', '--list-gpus'], 
                              capture_output=True, text=True, check=True)
        gpu_count = len(result.stdout.strip().split('\n'))
        print(f"检测到 {gpu_count} 个GPU")
        return gpu_count
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 无法检测GPU或nvidia-smi不可用")
        return 0

def test_distributed_import():
    """测试分布式训练相关的导入"""
    try:
        import torch
        import torch.distributed as dist
        from torch.nn.parallel import DistributedDataParallel as DDP
        from torch.utils.data.distributed import DistributedSampler
        print("✓ 分布式训练相关模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_nccl_availability():
    """测试NCCL后端可用性"""
    try:
        import torch
        if torch.cuda.is_available():
            # 简单测试NCCL是否可用
            if torch.distributed.is_nccl_available():
                print("✓ NCCL后端可用")
                return True
            else:
                print("✗ NCCL后端不可用")
                return False
        else:
            print("✗ CUDA不可用")
            return False
    except Exception as e:
        print(f"✗ NCCL测试失败: {e}")
        return False

def run_dry_run_test(num_gpus=2):
    """运行一个简单的分布式训练测试"""
    print(f"\n开始测试 {num_gpus} GPU分布式训练...")

    # 设置PYTHONPATH以包含src目录
    env = os.environ.copy()
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    src_dir = os.path.join(current_dir, 'src')
    if 'PYTHONPATH' in env:
        env['PYTHONPATH'] = f"{src_dir}:{env['PYTHONPATH']}"
    else:
        env['PYTHONPATH'] = src_dir

    # 创建一个最小的测试命令
    cmd = [
        "torchrun",
        "--standalone",
        "--nproc_per_node", str(num_gpus),
        "-m", "lerobot.scripts.train",
        "--help"  # 只显示帮助信息，不实际训练
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✓ 分布式训练脚本可以正常启动")
            return True
        else:
            print(f"✗ 分布式训练脚本启动失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("✗ 测试超时")
        return False
    except FileNotFoundError:
        print("✗ torchrun命令不存在，请确保PyTorch版本 >= 1.9")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    print("LeRobot 分布式训练功能测试")
    print("=" * 50)
    
    # 检查GPU
    gpu_count = check_gpu_availability()
    if gpu_count == 0:
        print("无GPU可用，无法进行分布式训练测试")
        sys.exit(1)
    
    # 测试导入
    if not test_distributed_import():
        print("分布式训练模块导入失败")
        sys.exit(1)
    
    # 测试NCCL
    if not test_nccl_availability():
        print("NCCL后端不可用，分布式训练可能无法正常工作")
    
    # 测试分布式训练脚本
    test_gpus = min(2, gpu_count)  # 最多测试2个GPU
    if run_dry_run_test(test_gpus):
        print(f"\n✓ 分布式训练功能测试通过！")
        print(f"你可以使用以下命令开始分布式训练:")
        print(f"./scripts/train_distributed.sh {gpu_count} --dataset.repo_id=your_dataset --policy.type=act")
    else:
        print(f"\n✗ 分布式训练功能测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
