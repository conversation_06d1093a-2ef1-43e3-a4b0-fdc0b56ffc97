#!/bin/bash

# 分布式训练启动脚本
# 使用方法: ./scripts/train_distributed.sh [GPU数量] [其他训练参数...]

# 检查参数
if [ $# -lt 1 ]; then
    echo "使用方法: $0 <GPU数量> [其他训练参数...]"
    echo "示例: $0 4 --dataset.repo_id=lerobot/pusht --policy.type=act --batch_size=32"
    exit 1
fi

# 获取GPU数量
NUM_GPUS=$1
shift  # 移除第一个参数，剩下的都是训练参数

# 检查GPU数量是否有效
if ! [[ "$NUM_GPUS" =~ ^[1-9][0-9]*$ ]]; then
    echo "错误: GPU数量必须是正整数"
    exit 1
fi

# 检查可用GPU数量
AVAILABLE_GPUS=$(nvidia-smi --list-gpus | wc -l)
if [ "$NUM_GPUS" -gt "$AVAILABLE_GPUS" ]; then
    echo "错误: 请求的GPU数量($NUM_GPUS)超过可用GPU数量($AVAILABLE_GPUS)"
    exit 1
fi

echo "开始分布式训练，使用 $NUM_GPUS 个GPU"
echo "训练参数: $@"

# 使用torchrun启动分布式训练
torchrun \
    --standalone \
    --nproc_per_node=$NUM_GPUS \
    -m lerobot.scripts.train \
    "$@"

echo "分布式训练完成"
