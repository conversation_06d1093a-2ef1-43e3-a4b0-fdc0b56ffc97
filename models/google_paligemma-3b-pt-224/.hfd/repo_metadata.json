{"_id": "6641487429be04778b9180f0", "id": "google/paligemma-3b-pt-224", "private": false, "pipeline_tag": "image-text-to-text", "library_name": "transformers", "tags": ["transformers", "safetensors", "paligemma", "image-text-to-text", "arxiv:2310.09199", "arxiv:2303.15343", "arxiv:2403.08295", "arxiv:1706.03762", "arxiv:2010.11929", "arxiv:2209.06794", "arxiv:2209.04372", "arxiv:2103.01913", "arxiv:2205.12522", "arxiv:2110.11624", "arxiv:2108.03353", "arxiv:2010.04295", "arxiv:2401.06209", "arxiv:2305.10355", "arxiv:2203.10244", "arxiv:1810.12440", "arxiv:1905.13648", "arxiv:1608.00272", "arxiv:1908.04913", "arxiv:2407.07726", "license:gemma", "text-generation-inference", "endpoints_compatible", "region:us"], "downloads": 45036, "likes": 332, "modelId": "google/paligemma-3b-pt-224", "author": "google", "sha": "35e4f46485b4d07967e7e9935bc3786aad50687c", "lastModified": "2024-09-21T10:14:25.000Z", "gated": "manual", "disabled": false, "model-index": null, "config": {"architectures": ["PaliGemmaForConditionalGeneration"], "model_type": "paligemma", "tokenizer_config": {"bos_token": "<bos>", "eos_token": "<eos>", "pad_token": "<pad>", "unk_token": "<unk>", "use_default_system_prompt": false}}, "cardData": {"library_name": "transformers", "license": "gemma", "pipeline_tag": "image-text-to-text", "extra_gated_heading": "Access PaliGemma on Hugging Face", "extra_gated_prompt": "To access PaliGemma on Hugging Face, you’re required to review and agree to Google’s usage license. To do this, please ensure you’re logged-in to Hugging Face and click below. Requests are processed immediately.", "extra_gated_button_content": "Acknowledge license"}, "transformersInfo": {"auto_model": "AutoModelForImageTextToText", "pipeline_tag": "image-text-to-text", "processor": "AutoProcessor"}, "siblings": [{"rfilename": ".gitattributes"}, {"rfilename": "README.md"}, {"rfilename": "added_tokens.json"}, {"rfilename": "config.json"}, {"rfilename": "generation_config.json"}, {"rfilename": "model-00001-of-00003.safetensors"}, {"rfilename": "model-00002-of-00003.safetensors"}, {"rfilename": "model-00003-of-00003.safetensors"}, {"rfilename": "model.safetensors.index.json"}, {"rfilename": "preprocessor_config.json"}, {"rfilename": "special_tokens_map.json"}, {"rfilename": "tokenizer.json"}, {"rfilename": "tokenizer.model"}, {"rfilename": "tokenizer_config.json"}], "spaces": ["Justinrune/LLaMA-Factory", "kenken999/fastapi_django_main_live", "pavel321/huggingface-cli-completion", "nsandiman/uarizona-msis-capstone-group5-imagecraft", "anthony-chen/Chem-210-Autograder", "Golu2811/drishti", "cosmo3769/finetuned-paligemma-vqav2-small", "pizb/GemmArte", "pyimagesearch/visual-question-answer-finetuned-paligemma", "davidr99/free_blackjack", "mc6666/QA", "msun415/Llamole", "fracapuano/remoteserver", "Tala2025/video_generation", "Ronaldodev/fastapi_django_main_live", "hardik<PERSON><PERSON>/tensora-autotrain", "ivangabriele/trl-sandbox"], "createdAt": "2024-05-12T22:53:40.000Z", "safetensors": {"parameters": {"F32": 2923466480}, "total": 2923466480}, "usedStorage": 23409790403}