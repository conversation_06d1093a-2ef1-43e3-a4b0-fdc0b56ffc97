#!/usr/bin/env python3
"""
ACT模型离线评估脚本

专门针对ACT模型的离线评估，不需要仿真环境。
支持动作预测准确性测试、每维度误差分析和详细的性能报告。

修复说明：
- 修复了错误的单步评估方式，现在使用正确的chunk评估方法
- 每个预测都与其对应时刻的真值比较，而不是用未来预测与当前真值比较

使用方法:
# 使用完整数据集评估
conda activate lerobot && python script/act_eval_random.py \
    --model_path outputs/train/pick_up_parts_less_act_chunk10_20250718_201017/checkpoints/last/pretrained_model \
    --dataset_repo_id pick_up_parts_less \
    --dataset_root /home/<USER>/data/testlerobot/lerobot \
    --n_samples 10 \
    --output_file scripts/act_eval_random_results.json

# 使用指定episodes评估
conda activate lerobot && python script/act_eval_random.py \
    --model_path outputs/train/pick_up_parts_less_act_chunk10_20250718_201017/checkpoints/last/pretrained_model \
    --dataset_repo_id pick_up_parts_less \
    --dataset_root /home/<USER>/data/testlerobot/lerobot \
    --episodes "[10,11,12,13,14]" \
    --n_samples 10 \
    --output_file scripts/act_eval_random_results.json
"""

import argparse
import json
import torch
import numpy as np
from pathlib import Path
from typing import Dict, List

from lerobot.datasets.lerobot_dataset import LeRobotDataset
from lerobot.policies.act.modeling_act import ACTPolicy
from lerobot.utils.utils import get_safe_torch_device


def parse_args():
    parser = argparse.ArgumentParser(description="ACT模型离线评估")
    parser.add_argument("--model_path", type=str, required=True,
                       help="训练好的ACT模型路径")
    parser.add_argument("--dataset_repo_id", type=str, required=True,
                       help="数据集repo ID")
    parser.add_argument("--dataset_root", type=str, required=True,
                       help="数据集根目录")
    parser.add_argument("--n_samples", type=int, default=50,
                       help="测试样本数 (默认: 50)")
    parser.add_argument("--device", type=str, default="cuda",
                       help="设备 (默认: cuda)")
    parser.add_argument("--output_file", type=str, default="act_eval_results.json",
                       help="输出结果文件 (默认: act_eval_results.json)")
    parser.add_argument("--episodes", type=str, default=None,
                       help="指定要评估的episodes，格式如 '[0,1,2]' 或 None 使用全部数据集")
    return parser.parse_args()


def evaluate_act_model(model: ACTPolicy, dataset: LeRobotDataset, indices: List[int], device: torch.device) -> Dict:
    """评估ACT模型在指定样本上的性能"""
    
    model.eval()
    successful_tests = 0
    action_errors = []
    per_dim_errors = []
    prediction_times = []
    
    print(f"开始评估 {len(indices)} 个样本...")
    
    with torch.no_grad():
        for i, idx in enumerate(indices):
            try:
                sample = dataset[idx]
                
                # 检查观测数据
                obs_keys = [k for k in sample.keys() if k.startswith('observation')]
                if not obs_keys:
                    continue
                
                # 准备观测数据
                obs_batch = {}
                for key in obs_keys:
                    if hasattr(sample[key], 'to'):
                        obs_batch[key] = sample[key].unsqueeze(0).to(device)
                    else:
                        obs_batch[key] = sample[key]
                
                # 测量预测时间
                start_time = torch.cuda.Event(enable_timing=True)
                end_time = torch.cuda.Event(enable_timing=True)
                
                start_time.record()
                pred_actions = model.predict_action_chunk(obs_batch)
                end_time.record()
                
                torch.cuda.synchronize()
                pred_time = start_time.elapsed_time(end_time)  # 毫秒
                prediction_times.append(pred_time)
                
                # 计算chunk评估误差（正确的方法）
                if 'action' in sample:
                    try:
                        # 获取预测的chunk
                        if len(pred_actions.shape) > 2:
                            # pred_actions shape: [batch_size, chunk_size, action_dim]
                            pred_chunk = pred_actions[0].cpu().numpy()  # [chunk_size, action_dim]
                            chunk_size = pred_chunk.shape[0]
                        else:
                            # 如果不是chunk预测，跳过
                            continue

                        # 获取对应的ground truth序列
                        gt_sequence = []
                        for j in range(chunk_size):
                            future_idx = idx + j
                            if future_idx < len(dataset):
                                try:
                                    future_sample = dataset[future_idx]
                                    if 'action' in future_sample:
                                        gt_action = future_sample['action']
                                        if len(gt_action.shape) > 1:
                                            gt_action = gt_action[-1, :]  # 取最后一个时间步
                                        gt_sequence.append(gt_action.cpu().numpy())
                                    else:
                                        break
                                except:
                                    break
                            else:
                                break

                        if len(gt_sequence) > 0:
                            gt_sequence = np.array(gt_sequence)
                            # 只使用有对应GT的部分
                            actual_chunk_size = min(len(gt_sequence), chunk_size)
                            pred_chunk_trimmed = pred_chunk[:actual_chunk_size]
                            gt_sequence_trimmed = gt_sequence[:actual_chunk_size]

                            # 计算chunk误差：每个预测与其对应时刻的真值比较
                            chunk_errors = []
                            chunk_dim_errors = []

                            for k in range(actual_chunk_size):
                                step_error = np.mean((pred_chunk_trimmed[k] - gt_sequence_trimmed[k]) ** 2)
                                dim_error = (pred_chunk_trimmed[k] - gt_sequence_trimmed[k]) ** 2
                                chunk_errors.append(step_error)
                                chunk_dim_errors.append(dim_error)

                            # 平均chunk误差
                            avg_chunk_error = np.mean(chunk_errors)
                            avg_dim_errors = np.mean(chunk_dim_errors, axis=0)

                            action_errors.append(avg_chunk_error)
                            per_dim_errors.append(avg_dim_errors)

                            # 在第一次处理时显示信息
                            if i == 0:
                                print(f"✅ 使用chunk评估模式:")
                                print(f"   Chunk size: {chunk_size}")
                                print(f"   实际评估长度: {actual_chunk_size}")
                                print(f"   每个预测与其对应时刻的真值比较")

                    except Exception as e:
                        print(f"样本 {idx} chunk评估失败: {e}")
                        continue
                    
                    successful_tests += 1
                    
                    if (i + 1) % 10 == 0:
                        print(f"已处理 {i + 1}/{len(indices)} 个样本")
                        
                else:
                    successful_tests += 1
                    
            except Exception as e:
                print(f"样本 {idx} 测试失败: {e}")
                continue
    
    # 计算统计指标
    results = {
        "total_samples": len(indices),
        "successful_tests": successful_tests,
        "success_rate": successful_tests / len(indices) * 100,
        "avg_prediction_time_ms": float(np.mean(prediction_times)) if prediction_times else 0.0,
    }
    
    if action_errors:
        results.update({
            "avg_mse_error": float(np.mean(action_errors)),
            "std_mse_error": float(np.std(action_errors)),
            "min_mse_error": float(np.min(action_errors)),
            "max_mse_error": float(np.max(action_errors)),
            "median_mse_error": float(np.median(action_errors)),
            "avg_rmse_error": float(np.sqrt(np.mean(action_errors))),  # 根均方误差
            "avg_angle_error_deg": float(np.sqrt(np.mean(action_errors)) * 180 / np.pi),  # 转换为角度
        })
        
        # 每维度误差分析
        if per_dim_errors:
            per_dim_errors = np.array(per_dim_errors)
            avg_per_dim_errors = np.mean(per_dim_errors, axis=0)
            results["per_dim_mse_errors"] = avg_per_dim_errors.tolist()
            results["per_dim_rmse_errors"] = np.sqrt(avg_per_dim_errors).tolist()
            results["per_dim_angle_errors_deg"] = (np.sqrt(avg_per_dim_errors) * 180 / np.pi).tolist()
            results["action_dim"] = int(len(avg_per_dim_errors))
    
    return results


def print_evaluation_results(results: Dict):
    """打印评估结果"""
    print("=" * 70)
    print("ACT模型离线评估结果")
    print("=" * 70)
    
    print(f"总测试样本: {results['total_samples']}")
    print(f"成功测试: {results['successful_tests']}")
    print(f"成功率: {results['success_rate']:.1f}%")
    print(f"平均预测时间: {results['avg_prediction_time_ms']:.2f} ms")
    
    if "avg_mse_error" in results:
        print(f"\n动作预测误差分析:")
        print(f"  平均MSE误差: {results['avg_mse_error']:.6f}")
        print(f"  平均RMSE误差: {results['avg_rmse_error']:.6f} 弧度")
        print(f"  平均角度误差: {results['avg_angle_error_deg']:.2f} 度")
        print(f"  误差标准差: {results['std_mse_error']:.6f}")
        print(f"  最小误差: {results['min_mse_error']:.6f}")
        print(f"  最大误差: {results['max_mse_error']:.6f}")
        print(f"  中位数误差: {results['median_mse_error']:.6f}")
        
        # 性能评估
        avg_angle_error = results['avg_angle_error_deg']
        print(f"\n性能评估:")
        if avg_angle_error < 5:
            print("✅ 角度误差很小，模型精度高")
        elif avg_angle_error < 15:
            print("⚠️  角度误差中等，模型性能良好")
        elif avg_angle_error < 30:
            print("⚠️  角度误差较大，模型性能一般")
        else:
            print("❌ 角度误差很大，模型需要改进")
        
        # 每维度误差
        if "per_dim_angle_errors_deg" in results:
            print(f"\n每维度角度误差 (共{results['action_dim']}维):")
            for i, error in enumerate(results["per_dim_angle_errors_deg"]):
                status = "✅" if error < 10 else "⚠️" if error < 20 else "❌"
                print(f"  维度 {i:2d}: {error:6.2f}° {status}")
    
    print("=" * 70)


def main():
    args = parse_args()
    
    print("=" * 70)
    print("ACT模型离线评估 (Chunk评估模式)")
    print("=" * 70)
    print(f"模型路径: {args.model_path}")
    print(f"数据集: {args.dataset_repo_id}")
    print(f"测试样本数: {args.n_samples}")
    print(f"设备: {args.device}")
    print(f"Episodes: {args.episodes if args.episodes else '完整数据集'}")
    print(f"评估方式: 整体chunk评估（每个预测与其对应时刻的真值比较）")
    print("=" * 70)
    
    # 检查设备
    device = get_safe_torch_device(args.device, log=True)
    
    try:
        # 加载模型
        print("正在加载ACT模型...")
        model = ACTPolicy.from_pretrained(args.model_path)
        model.eval()
        model.to(device)
        print(f"模型加载成功: {type(model).__name__}")
        
        # 加载数据集
        print("正在加载数据集...")

        # 解析episodes参数
        episodes = None
        if args.episodes:
            try:
                import ast
                episodes = ast.literal_eval(args.episodes)
                if not isinstance(episodes, list):
                    raise ValueError("episodes必须是列表格式")
                print(f"使用指定的episodes: {episodes}")
            except Exception as e:
                print(f"解析episodes参数失败: {e}")
                print("使用完整数据集")
                episodes = None

        dataset = LeRobotDataset(args.dataset_repo_id, root=args.dataset_root, episodes=episodes)
        if episodes:
            print(f"数据集大小: {len(dataset)} 个样本 (来自 {len(episodes)} 个episodes)")
        else:
            print(f"数据集大小: {len(dataset)} 个样本 (完整数据集)")
        
        # 随机选择样本
        indices = np.random.choice(len(dataset), min(args.n_samples, len(dataset)), replace=False)
        indices = [int(idx) for idx in indices]  # 转换为Python int
        
        # 评估模型
        results = evaluate_act_model(model, dataset, indices, device)
        
        # 添加配置信息
        results["config"] = {
            "model_path": args.model_path,
            "dataset_repo_id": args.dataset_repo_id,
            "dataset_root": args.dataset_root,
            "n_samples": args.n_samples,
            "device": str(device),
            "episodes": args.episodes,
            "eval_mode": "chunk",
        }
        
        # 打印结果
        print_evaluation_results(results)
        
        # 保存结果
        output_path = Path(args.output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n结果已保存到: {output_path}")
        
    except Exception as e:
        print(f"评估过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
