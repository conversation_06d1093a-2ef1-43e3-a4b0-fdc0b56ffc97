#!/usr/bin/env python3
"""
Parquet joint data visualization tool
Plot action and state values for comparison, right arm joints only
X-axis: time steps, Y-axis: joint angle values

Usage:
python plot_parquet_joints.py /path/to/file.parquet
python plot_parquet_joints.py /path/to/file.parquet --max_frames 1000
python plot_parquet_joints.py /path/to/file.parquet --output_dir plots
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import argparse
import os
import logging
from pathlib import Path

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def plot_joint_comparison(parquet_file, output_dir=None, max_frames=None):
    """
    Plot action and state joint angle comparison for right arm joints

    Args:
        parquet_file: path to parquet file
        output_dir: output directory, default is current directory
        max_frames: maximum frame limit, None means use all data
    """
    if output_dir is None:
        output_dir = "."
    
    os.makedirs(output_dir, exist_ok=True)

    # Read parquet file
    logging.info(f"Reading parquet file: {parquet_file}")
    df = pd.read_parquet(parquet_file)
    total_frames = len(df)
    logging.info(f"Total frames: {total_frames}")

    # Limit frame count
    if max_frames and max_frames < total_frames:
        df = df.head(max_frames)
        logging.info(f"Limited to first {max_frames} frames")

    # Find action and state columns
    action_cols = [col for col in df.columns if 'action' in col.lower()]
    state_cols = [col for col in df.columns if 'state' in col.lower() or 'observation.state' in col]

    logging.info(f"Found action columns: {action_cols}")
    logging.info(f"Found state columns: {state_cols}")

    if not action_cols or not state_cols:
        logging.error("No action or state columns found")
        return
    
    # Extract data
    action_data = None
    state_data = None

    # Process action data
    if len(action_cols) == 1:
        # Single column, might be array
        sample_action = df[action_cols[0]].iloc[0]
        if isinstance(sample_action, (list, np.ndarray)):
            # Array format, expand
            action_data = np.array([np.array(row) for row in df[action_cols[0]]])
            logging.info(f"Action data shape: {action_data.shape}")
        else:
            logging.error("Action column is not array format")
            return
    else:
        # Multiple columns, one per joint
        action_data = df[action_cols].values
        logging.info(f"Action data shape: {action_data.shape}")

    # Process state data
    if len(state_cols) == 1:
        # Single column, might be array
        sample_state = df[state_cols[0]].iloc[0]
        if isinstance(sample_state, (list, np.ndarray)):
            # Array format, expand
            state_data = np.array([np.array(row) for row in df[state_cols[0]]])
            logging.info(f"State data shape: {state_data.shape}")
        else:
            logging.error("State column is not array format")
            return
    else:
        # Multiple columns, one per joint
        state_data = df[state_cols].values
        logging.info(f"State data shape: {state_data.shape}")

    # Ensure data dimensions match
    if action_data.shape[1] != state_data.shape[1]:
        logging.warning(f"Action and State dimensions mismatch: {action_data.shape[1]} vs {state_data.shape[1]}")
        min_dims = min(action_data.shape[1], state_data.shape[1])
        action_data = action_data[:, :min_dims]
        state_data = state_data[:, :min_dims]
        logging.info(f"Truncated to same dimension: {min_dims}")
    
    num_joints = action_data.shape[1]
    num_frames = action_data.shape[0]

    # Create time axis with detailed scale
    time_steps = np.arange(num_frames)

    # Only show right arm joints (joints 7-13: right arm joints 1-6 and right gripper)
    right_arm_indices = list(range(7, 14))  # joints 7-13

    # Create 7x1 subplots, larger size
    fig, axes = plt.subplots(7, 1, figsize=(16, 24))
    fig.suptitle(f'Right Arm Joint Angle Comparison - {Path(parquet_file).name}\nTotal frames: {num_frames}, Joint count: {num_joints}',
                 fontsize=16, fontweight='bold')

    # Right arm joint names
    right_arm_names = [
        "Right Joint 1", "Right Joint 2", "Right Joint 3", "Right Joint 4", "Right Joint 5", "Right Joint 6", "Right Gripper"
    ]

    # Plot right arm joint data
    for plot_idx in range(7):
        ax = axes[plot_idx]
        joint_idx = right_arm_indices[plot_idx]

        if joint_idx < num_joints:
            # Plot action and state, thinner lines, no filtering
            ax.plot(time_steps, action_data[:, joint_idx], 'b-', label='Action', linewidth=0.8, alpha=1.0)
            ax.plot(time_steps, state_data[:, joint_idx], 'r--', label='State', linewidth=0.8, alpha=1.0)

            # Set title and labels
            ax.set_title(f'{right_arm_names[plot_idx]} (Joint {joint_idx+1})', fontsize=12, fontweight='bold')
            ax.set_ylabel('Angle Value', fontsize=11)
            ax.grid(True, alpha=0.3)

            # Add legend (only on first subplot)
            if plot_idx == 0:
                ax.legend(loc='upper right', fontsize=11)

            # Set y-axis range
            y_min = min(np.min(action_data[:, joint_idx]), np.min(state_data[:, joint_idx]))
            y_max = max(np.max(action_data[:, joint_idx]), np.max(state_data[:, joint_idx]))
            y_range = y_max - y_min
            if y_range > 0:
                ax.set_ylim(y_min - y_range*0.1, y_max + y_range*0.1)

            # Set detailed x-axis ticks for time calculation
            if num_frames > 100:
                # For large datasets, show every 50 frames
                tick_interval = max(50, num_frames // 20)
            else:
                # For small datasets, show every 10 frames
                tick_interval = max(10, num_frames // 10)

            x_ticks = np.arange(0, num_frames, tick_interval)
            if x_ticks[-1] != num_frames - 1:
                x_ticks = np.append(x_ticks, num_frames - 1)

            ax.set_xticks(x_ticks)

        else:
            # Empty subplot
            ax.set_title(f'{right_arm_names[plot_idx]} (No Data)', fontsize=12, color='gray')
            ax.set_ylabel('Angle Value', fontsize=11)
            ax.grid(True, alpha=0.3)
            ax.text(0.5, 0.5, 'No Data', transform=ax.transAxes,
                   ha='center', va='center', fontsize=14, color='gray')

        # Only show x-axis labels on the last subplot
        if plot_idx == 6:
            ax.set_xlabel('Time Steps (Frame Index)', fontsize=12)
        else:
            ax.set_xticklabels([])
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(top=0.95)

    # Save plot
    output_filename = f"{Path(parquet_file).stem}_right_arm_joints.png"
    output_path = os.path.join(output_dir, output_filename)
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    logging.info(f"Plot saved to: {output_path}")

    # Show plot
    plt.show()

    # Print statistics
    print("\n=== Data Statistics ===")
    print(f"Total frames: {num_frames}")
    print(f"Joint count: {num_joints}")
    print(f"Action data range: [{np.min(action_data):.4f}, {np.max(action_data):.4f}]")
    print(f"State data range: [{np.min(state_data):.4f}, {np.max(state_data):.4f}]")
    print(f"Time scale: Each frame represents 1 time step")
    print(f"Total time span: 0 to {num_frames-1} time steps")

    # Calculate right arm joint differences
    print("\n=== Right Arm Joint Action-State Difference Statistics ===")
    right_arm_names_stats = [
        "Right Joint 1", "Right Joint 2", "Right Joint 3", "Right Joint 4", "Right Joint 5", "Right Joint 6", "Right Gripper"
    ]
    for plot_idx, joint_idx in enumerate(right_arm_indices):
        if joint_idx < num_joints:
            diff = action_data[:, joint_idx] - state_data[:, joint_idx]
            print(f"{right_arm_names_stats[plot_idx]:>15}: Mean diff={np.mean(diff):>8.4f}, Std dev={np.std(diff):>8.4f}, Max diff={np.max(np.abs(diff)):>8.4f}")
        else:
            print(f"{right_arm_names_stats[plot_idx]:>15}: No data")

def main():
    parser = argparse.ArgumentParser(description='Visualize joint data from parquet file (right arm only)')
    parser.add_argument('parquet_file', help='Path to parquet file')
    parser.add_argument('--output_dir', default='.', help='Output directory, default is current directory')
    parser.add_argument('--max_frames', type=int, help='Maximum frame limit for quick preview of large files')

    args = parser.parse_args()

    # Setup logging
    setup_logging()

    # Check if file exists
    if not os.path.exists(args.parquet_file):
        logging.error(f"File does not exist: {args.parquet_file}")
        return

    # Plot charts
    plot_joint_comparison(args.parquet_file, args.output_dir, args.max_frames)

if __name__ == "__main__":
    main()
