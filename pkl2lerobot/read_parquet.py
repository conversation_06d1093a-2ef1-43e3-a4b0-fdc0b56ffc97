"""
conda activate lerobot;
python read_parquet.py   /home/<USER>/data1/pick_v_test/openpi\
     -o pick_v  -num 10
     
# num_workers 根据内存定， 10个稳定

程序会解析parquet文件的所有行，将数据保存到Excel，并将图像提取为PNG文件。
使用OpenCV进行图像解码和处理，保持原始通道顺序一致性。
支持多进程并行处理多个parquet文件，大幅提升处理速度。
"""
import pandas as pd
import numpy as np
import os
import cv2
import io
import glob
import logging
import sys
from datetime import datetime
import argparse
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing
from PIL import Image
import pickle

def setup_logging(output_dir):
    """设置日志记录到文件，确保实时写入"""
    # 创建日志目录
    log_dir = os.path.join(output_dir, "logs")
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"parquet_read_{timestamp}.log")
    
    # 配置日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, mode='w'),  # 使用'w'模式覆盖文件
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    
    # 确保每次日志都立即刷新到文件
    for handler in root_logger.handlers:
        if isinstance(handler, logging.FileHandler):
            handler.flush = lambda: handler.stream.flush()
    
    # 创建一个自定义的print函数，同时打印到控制台和日志文件
    original_print = print
    
    def custom_print(*args, **kwargs):
        # 调用原始print函数
        original_print(*args, **kwargs)
        # 将相同的消息写入日志
        message = " ".join(str(arg) for arg in args)
        logging.info(message)
    
    # 替换全局print函数
    sys.modules['builtins'].print = custom_print
    
    logging.info(f"日志将被记录到: {log_file}")
    return log_file

def process_single_file(args):
    """
    处理单个parquet文件的包装函数，用于多进程调用

    Args:
        args: (parquet_file, output_dir) 元组

    Returns:
        处理结果信息
    """
    parquet_file, output_dir = args
    try:
        result = read_all_rows(parquet_file, output_dir)
        return f"成功处理: {parquet_file}"
    except Exception as e:
        return f"处理失败: {parquet_file}, 错误: {str(e)}"

def read_all_rows(parquet_file, output_dir=None):
    """
    读取parquet文件的所有行并保存到Excel，同时提取图像

    Args:
        parquet_file: parquet文件路径
        output_dir: 输出目录，默认为"output"

    Returns:
        输出的Excel文件路径
    """
    # 如果未指定输出目录，则创建一个
    if output_dir is None:
        output_dir = "output"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置日志
    log_file = setup_logging(output_dir)
    
    # 从parquet文件名创建输出文件名
    parquet_basename = os.path.basename(parquet_file)
    parquet_name = os.path.splitext(parquet_basename)[0]
    output_excel = os.path.join(output_dir, f"{parquet_name}.xlsx")
    
    # 创建图像输出目录
    images_dir = os.path.join(output_dir, f"{parquet_name}_images")
    os.makedirs(images_dir, exist_ok=True)
    
    # 读取全部数据
    df_full = pd.read_parquet(parquet_file)
    total_rows = len(df_full)
    logging.info(f"文件 {parquet_file} 总行数: {total_rows}")

    # 处理数据函数
    def process_dataframe(df, section_name):
        # 创建新的DataFrame来存储展开后的数据
        expanded_data = {}
        
        # 遍历所有列
        for col in df.columns:
            # 处理数值数组列（如action）
            sample_value = df[col].iloc[0]
            if isinstance(sample_value, (list, np.ndarray)) and len(sample_value) > 0:
                # 为数组中的每个元素创建单独的列
                for i in range(len(sample_value)):
                    expanded_data[f"{col}_{i}"] = df[col].apply(lambda x: x[i] if isinstance(x, (list, np.ndarray)) and i < len(x) else None)
            elif 'image' in col.lower() or 'rgb' in col.lower():
                # 对于图像列，创建空列
                expanded_data[col] = None
                # 处理图像并保存为PNG
                for idx, row in df.iterrows():
                    img_data = row[col]
                    if img_data is not None:
                        try:
                            # 尝试解码图像
                            img_cv = None
                            if isinstance(img_data, bytes):
                                # pickle序列化的图像数据，需要反序列化
                                try:
                                    img_array = pickle.loads(img_data)
                                    if isinstance(img_array, np.ndarray) and len(img_array.shape) == 3:
                                        # 确保数据类型正确
                                        img_cv = img_array.astype(np.uint8)
                                        logging.info(f"成功反序列化图像数据: {img_cv.shape}, dtype={img_cv.dtype}")
                                    else:
                                        logging.warning(f"反序列化后的数据格式不正确: {type(img_array)}, shape={getattr(img_array, 'shape', 'N/A')}")
                                except Exception as e:
                                    logging.error(f"反序列化图像数据失败: {e}")
                                    continue
                            elif isinstance(img_data, dict) and 'bytes' in img_data:
                                # 字节数据，需要解码
                                img_bytes = img_data['bytes']
                                # 使用OpenCV解码，保持原始通道顺序
                                img_array = np.frombuffer(img_bytes, dtype=np.uint8)
                                img_cv = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                            elif isinstance(img_data, np.ndarray):
                                if len(img_data.shape) == 3:
                                    # 已经是图像数组，直接使用
                                    # 确保数据类型正确
                                    img_cv = img_data.astype(np.uint8)
                                elif len(img_data.shape) == 1:
                                    # 可能是压缩的图像数据
                                    img_array = img_data.astype(np.uint8)
                                    img_cv = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                            else:
                                logging.warning(f"未知的图像数据格式: {type(img_data)}")
                                continue

                            if img_cv is not None:
                                # 使用列名作为图像文件名的一部分
                                img_filename = f"{col.replace('.', '_')}_row{idx}.png"
                                img_path = os.path.join(images_dir, img_filename)
                                # 使用OpenCV保存，保持原始通道顺序
                                # debug 
                                # if img_cv is not None and img_cv.shape[2] == 3:
                                #     img_cv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB)
                                #     print(f"从cvt bgr2rgb转换{col}图像: {img_cv.shape}")
                                #     cv2.imwrite(img_path, img_cv)
                                #     import pdb ;    pdb.set_trace()

                                cv2.imwrite(img_path, img_cv)
                              
                                logging.info(f"保存图像: {img_path} (尺寸: {img_cv.shape}, OpenCV格式)")
                        except Exception as e:
                            logging.error(f"处理图像时出错: {e}")
            else:
                # 保留非数组列
                expanded_data[col] = df[col]
        
        # 创建新的DataFrame
        expanded_df = pd.DataFrame(expanded_data)
        return expanded_df
    
    # 处理所有行
    expanded_all = process_dataframe(df_full, "all_rows")
    logging.info(f"\n所有 {total_rows} 行数据处理完成")
    
    # 将数据写入Excel文件，使用float_format保留完整精度
    with pd.ExcelWriter(output_excel, engine='openpyxl') as writer:
        # 设置不进行四舍五入，保留完整精度
        float_format = '%.15f'  # 保留15位小数，不进行四舍五入

        expanded_all.to_excel(writer, sheet_name="所有数据", float_format=float_format)
        
        # 添加一个包含文件信息的sheet
        info_df = pd.DataFrame({
            "属性": ["文件路径", "总行数", "列数", "列名", "图像保存目录", "日志文件"],
            "值": [
                parquet_file, 
                total_rows, 
                len(df_full.columns),
                ", ".join(df_full.columns),
                images_dir,
                log_file
            ]
        })
        info_df.to_excel(writer, sheet_name="文件信息", index=False)
    
    logging.info(f"数据已保存到Excel文件: {output_excel}")
    logging.info(f"图像已保存到目录: {images_dir}")
    logging.info(f"日志文件保存在: {log_file}")
    
    return output_excel

def process_directory(input_dir, output_dir=None, num_workers=None):
    """
    处理目录中的所有parquet文件

    Args:
        input_dir: 包含parquet文件的目录
        output_dir: 输出目录，默认为"output"
        num_workers: 进程数，默认为CPU核心数
    """
    if output_dir is None:
        output_dir = "output"

    if num_workers is None:
        num_workers = multiprocessing.cpu_count()

    os.makedirs(output_dir, exist_ok=True)

    # 设置日志
    log_file = setup_logging(output_dir)

    # 查找目录中的所有parquet文件
    parquet_files = []
    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.endswith('.parquet'):
                parquet_files.append(os.path.join(root, file))

    if not parquet_files:
        logging.warning(f"在目录 {input_dir} 中未找到parquet文件")
        return

    logging.info(f"找到 {len(parquet_files)} 个parquet文件")
    logging.info(f"使用 {num_workers} 个进程进行并行处理")

    # 准备参数列表
    args_list = [(parquet_file, output_dir) for parquet_file in parquet_files]

    # 使用多进程处理
    if num_workers == 1:
        # 单进程处理
        for i, args in enumerate(args_list):
            logging.info(f"\n处理文件 {i+1}/{len(args_list)}: {args[0]}")
            result = process_single_file(args)
            logging.info(result)
    else:
        # 多进程处理
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            # 提交所有任务
            future_to_file = {executor.submit(process_single_file, args): args[0]
                            for args in args_list}

            # 收集结果
            completed = 0
            for future in as_completed(future_to_file):
                completed += 1
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    logging.info(f"[{completed}/{len(parquet_files)}] {result}")
                except Exception as e:
                    logging.error(f"[{completed}/{len(parquet_files)}] 处理文件 {file_path} 时出错: {e}")

    logging.info(f"\n所有文件处理完成，输出保存在: {output_dir}")
    logging.info(f"日志文件保存在: {log_file}")

def main():
    parser = argparse.ArgumentParser(description='读取parquet文件的所有行并保存到Excel')
    parser.add_argument('input_path', help='parquet文件路径或包含parquet文件的目录')
    parser.add_argument('--output_dir','-o' ,help='输出目录，默认为"output"')
    parser.add_argument('--num_workers','-num', type=int, default=None,
                       help=f'并行处理的进程数，默认为CPU核心数({multiprocessing.cpu_count()})')

    args = parser.parse_args()

    # 验证进程数参数
    if args.num_workers is not None and args.num_workers < 1:
        print("错误: 进程数必须大于0")
        return

    # 检查输入路径是文件还是目录
    if os.path.isfile(args.input_path) and args.input_path.endswith('.parquet'):
        # 处理单个文件
        print(f"处理单个文件: {args.input_path}")
        read_all_rows(args.input_path, args.output_dir)
    elif os.path.isdir(args.input_path):
        # 处理目录
        print(f"处理目录: {args.input_path}")
        if args.num_workers:
            print(f"使用 {args.num_workers} 个进程")
        else:
            print(f"使用默认进程数: {multiprocessing.cpu_count()}")
        process_directory(args.input_path, args.output_dir, args.num_workers)
    else:
        print(f"错误: {args.input_path} 不是有效的parquet文件或目录")

if __name__ == "__main__":
    # 确保多进程在所有平台上正常工作
    multiprocessing.set_start_method('spawn', force=True)
    main()
