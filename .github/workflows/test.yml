# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Tests

on:
  pull_request:
    paths:
      - "src/**"
      - "tests/**"
      - "examples/**"
      - ".github/**"
      - "pyproject.toml"
      - ".pre-commit-config.yaml"
      - "Makefile"
      - ".cache/**"
  push:
    branches:
      - main
    paths:
      - "src/**"
      - "tests/**"
      - "examples/**"
      - ".github/**"
      - "pyproject.toml"
      - ".pre-commit-config.yaml"
      - "Makefile"
      - ".cache/**"

permissions: {}

env:
  UV_VERSION: "0.6.0"

jobs:
  pytest:
    name: Pytest
    runs-on: ubuntu-latest
    env:
      MUJOCO_GL: egl
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          lfs: true  # Ensure LFS files are pulled
          persist-credentials: false

      - name: Install apt dependencies
      # portaudio19-dev is needed to install pyaudio
        run: |
          sudo apt-get update && \
          sudo apt-get install -y libegl1-mesa-dev ffmpeg portaudio19-dev

      - name: Install uv and python
        uses: astral-sh/setup-uv@d4b2f3b6ecc6e67c4457f6d3e41ec42d3d0fcb86 # v5.4.2
        with:
          enable-cache: true
          version: ${{ env.UV_VERSION }}
          python-version: "3.10"

      - name: Install lerobot (all extras)
        run: uv sync --all-extras

      - name: Test with pytest
        run: |
          uv run pytest tests -v --cov=./src/lerobot --durations=0 \
            -W ignore::DeprecationWarning:imageio_ffmpeg._utils:7 \
            -W ignore::UserWarning:torch.utils.data.dataloader:558 \
            -W ignore::UserWarning:gymnasium.utils.env_checker:247 \
            && rm -rf tests/outputs outputs

  pytest-minimal:
    name: Pytest (minimal install)
    runs-on: ubuntu-latest
    env:
      MUJOCO_GL: egl
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          lfs: true  # Ensure LFS files are pulled
          persist-credentials: false

      - name: Install apt dependencies
        run: sudo apt-get update && sudo apt-get install -y ffmpeg

      - name: Install uv and python
        uses: astral-sh/setup-uv@d4b2f3b6ecc6e67c4457f6d3e41ec42d3d0fcb86 # v5.4.2
        with:
          enable-cache: true
          version: ${{ env.UV_VERSION }}
          python-version: "3.10"

      - name: Install lerobot
        run: uv sync --extra "test"

      - name: Test with pytest
        run: |
          uv run pytest tests -v --cov=./src/lerobot --durations=0 \
            -W ignore::DeprecationWarning:imageio_ffmpeg._utils:7 \
            -W ignore::UserWarning:torch.utils.data.dataloader:558 \
            -W ignore::UserWarning:gymnasium.utils.env_checker:247 \
            && rm -rf tests/outputs outputs

  end-to-end:
    name: End-to-end
    runs-on: ubuntu-latest
    env:
      MUJOCO_GL: egl
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          lfs: true  # Ensure LFS files are pulled
          persist-credentials: false

      - name: Install apt dependencies
      # portaudio19-dev is needed to install pyaudio
        run: |
          sudo apt-get update && \
          sudo apt-get install -y libegl1-mesa-dev ffmpeg portaudio19-dev

      - name: Install uv and python
        uses: astral-sh/setup-uv@d4b2f3b6ecc6e67c4457f6d3e41ec42d3d0fcb86 # v5.4.2
        with:
          enable-cache: true
          version: ${{ env.UV_VERSION }}
          python-version: "3.10"

      - name: Install lerobot (all extras)
        run: |
          uv venv
          uv sync --all-extras

      - name: venv
        run: |
          echo "PYTHON_PATH=${{ github.workspace }}/.venv/bin/python" >> $GITHUB_ENV

      - name: Test end-to-end
        run: |
          make test-end-to-end \
            && rm -rf outputs
